# 开发环境配置指南

## 概述

本项目的开发环境已配置为使用本地MySQL数据库，以更好地匹配生产环境。数据会持久化保存，重新编译不会丢失数据。

## 环境要求

- Docker Desktop
- Java 17+
- Maven 3.6+

## 快速启动

### 1. 启动开发环境

```bash
# 一键启动数据库和应用
./start-dev.sh
```

这个脚本会：
- 启动MySQL数据库容器
- 启动Redis缓存容器
- 启动phpMyAdmin管理工具
- 编译并启动Spring Boot应用

### 2. 停止开发环境

```bash
# 停止所有服务（保留数据）
./stop-dev.sh

# 完全清理（包括数据）
docker-compose -f docker-compose.dev.yml down -v
```

## 服务访问地址

### 应用服务
- **API文档**: http://localhost:8081/api/v1/swagger-ui.html
- **健康检查**: http://localhost:8081/api/v1/actuator/health
- **API基础路径**: http://localhost:8081/api/v1

### 数据库服务
- **MySQL**: localhost:3306
  - 数据库: `rtpos_dev`
  - 管理员: `root` / `password`
  - 开发用户: `rtpos_dev` / `rtpos_dev123`

- **Redis**: localhost:6379
  - 数据库: 1

- **phpMyAdmin**: http://localhost:8083
  - 用户名: `root`
  - 密码: `password`

## 数据库配置详情

### 开发环境 vs 生产环境对比

| 配置项 | 开发环境 | 生产环境 |
|--------|----------|----------|
| 数据库类型 | MySQL 8.0 | MySQL 8.0 |
| 数据库名 | rtpos_dev | rtpos_db |
| 端口 | 3306 | 3306 |
| DDL策略 | update | validate |
| SQL日志 | 启用 | 禁用 |
| 连接池大小 | 20 | 50 |

### 数据持久化

- 数据存储在Docker卷中：`mysql_dev_data`
- 重启容器或重新编译应用都不会丢失数据
- 只有执行 `docker-compose down -v` 才会清除数据

## 开发工作流

### 日常开发

1. 启动开发环境：`./start-dev.sh`
2. 进行代码修改
3. 应用会自动重启（Spring Boot DevTools）
4. 数据保持不变

### 数据库管理

1. **通过phpMyAdmin**：
   - 访问 http://localhost:8080
   - 可视化管理数据库

2. **通过命令行**：
   ```bash
   # 连接到MySQL容器
   docker exec -it rtpos-mysql-dev mysql -uroot -ppassword rtpos_dev
   ```

3. **通过应用API**：
   - 使用Swagger UI测试API
   - 通过API操作数据

### 数据重置

如果需要重置开发数据：

```bash
# 停止服务并清除数据
docker-compose -f docker-compose.dev.yml down -v

# 重新启动
./start-dev.sh
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3306
   lsof -i :6379
   lsof -i :8080
   lsof -i :8081
   ```

2. **MySQL启动失败**
   ```bash
   # 查看MySQL日志
   docker logs rtpos-mysql-dev
   
   # 重启MySQL容器
   docker restart rtpos-mysql-dev
   ```

3. **应用连接数据库失败**
   - 确保MySQL容器正在运行
   - 检查数据库连接配置
   - 查看应用日志

### 日志查看

```bash
# 查看MySQL日志
docker logs rtpos-mysql-dev

# 查看Redis日志
docker logs rtpos-redis-dev

# 查看应用日志
tail -f logs/rtpos-server.log
```

## 配置自定义

### 修改数据库密码

1. 编辑 `docker-compose.dev.yml`
2. 修改 `MYSQL_ROOT_PASSWORD` 和相关密码
3. 更新 `src/main/resources/application-dev.yml` 中的密码
4. 重新启动服务

### 修改端口

1. 编辑 `docker-compose.dev.yml` 修改容器端口映射
2. 编辑 `src/main/resources/application-dev.yml` 修改应用端口
3. 重新启动服务

## 优势

✅ **数据持久化**: 重新编译不会丢失数据  
✅ **环境一致性**: 与生产环境使用相同的数据库类型  
✅ **易于管理**: 提供可视化数据库管理工具  
✅ **快速启动**: 一键启动所有必需服务  
✅ **隔离性**: 开发环境完全独立，不影响其他项目  

## 注意事项

⚠️ **Docker要求**: 需要安装并运行Docker Desktop  
⚠️ **资源占用**: MySQL容器会占用一定的系统资源  
⚠️ **数据备份**: 重要数据建议定期备份  
⚠️ **端口冲突**: 确保相关端口未被其他服务占用
