#!/bin/bash

# 开发环境检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    开发环境配置检查${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查Docker
echo -e "${YELLOW}🔍 检查Docker...${NC}"
if command -v docker &> /dev/null; then
    if docker info > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker运行正常${NC}"
        docker --version
    else
        echo -e "${RED}❌ Docker未运行，请启动Docker Desktop${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Docker未安装${NC}"
    exit 1
fi

echo ""

# 检查Java
echo -e "${YELLOW}🔍 检查Java...${NC}"
if command -v java &> /dev/null; then
    java_version=$(java -version 2>&1 | head -n 1)
    echo -e "${GREEN}✅ Java已安装${NC}"
    echo "    $java_version"
else
    echo -e "${RED}❌ Java未安装${NC}"
    exit 1
fi

echo ""

# 检查Maven
echo -e "${YELLOW}🔍 检查Maven...${NC}"
if [ -f "./mvnw" ]; then
    echo -e "${GREEN}✅ Maven包装器存在${NC}"
    ./mvnw --version | head -n 1
else
    echo -e "${RED}❌ Maven包装器不存在${NC}"
    exit 1
fi

echo ""

# 检查配置文件
echo -e "${YELLOW}🔍 检查配置文件...${NC}"

files=(
    "docker-compose.dev.yml"
    "init-dev.sql"
    "src/main/resources/application-dev.yml"
    "start-dev.sh"
    "stop-dev.sh"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file 不存在${NC}"
    fi
done

echo ""

# 检查端口占用
echo -e "${YELLOW}🔍 检查端口占用...${NC}"

ports=(3306 6379 8080 8081)
for port in "${ports[@]}"; do
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $port 已被占用${NC}"
        lsof -i :$port | head -n 2
    else
        echo -e "${GREEN}✅ 端口 $port 可用${NC}"
    fi
done

echo ""

# 检查Docker卷
echo -e "${YELLOW}🔍 检查Docker卷...${NC}"
if docker volume ls | grep -q "mysql_dev_data"; then
    echo -e "${YELLOW}⚠️  MySQL数据卷已存在（包含历史数据）${NC}"
else
    echo -e "${GREEN}✅ MySQL数据卷不存在（全新环境）${NC}"
fi

if docker volume ls | grep -q "redis_dev_data"; then
    echo -e "${YELLOW}⚠️  Redis数据卷已存在（包含历史数据）${NC}"
else
    echo -e "${GREEN}✅ Redis数据卷不存在（全新环境）${NC}"
fi

echo ""

# 检查运行中的容器
echo -e "${YELLOW}🔍 检查运行中的相关容器...${NC}"
if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(mysql|redis|phpmyadmin)" > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  发现运行中的相关容器：${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(mysql|redis|phpmyadmin)"
else
    echo -e "${GREEN}✅ 没有运行中的相关容器${NC}"
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}🎉 环境检查完成！${NC}"
echo ""
echo -e "${BLUE}下一步操作：${NC}"
echo -e "  1. 运行 ${YELLOW}./start-dev.sh${NC} 启动开发环境"
echo -e "  2. 访问 ${YELLOW}http://localhost:8081/api/v1/swagger-ui.html${NC} 查看API文档"
echo -e "  3. 访问 ${YELLOW}http://localhost:8080${NC} 管理数据库"
echo ""
